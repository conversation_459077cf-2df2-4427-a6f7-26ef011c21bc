import { define<PERSON>hai<PERSON> } from 'viem'
 
export const ecrox = define<PERSON>hain({
  id: 278356,
  name: 'Ecrox Chain',
  nativeCurrency: {
    decimals: 18,
    name: 'ECROX',
    symbol: 'ECROX',
  },
  rpcUrls: {
    default: {
      http: ['https://testnet-rpc.ecroxscan.com'],
      webSocket: ['wss://testnet-rpc.ecroxscan.com'],
    },
  },
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://testnet-explorer.ecroxscan.com' },
  },
  contracts: {
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
      blockCreated: 5882,
    },
  },
})