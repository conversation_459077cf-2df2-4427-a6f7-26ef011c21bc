<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Resume</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            line-height: 1.4;
            background-color: #f5f5f5;
            color: #333;
        }

        .resume-container {
            background-color: white;
            padding: 40px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 20px;
        }

        .header-left {
            flex: 1;
        }

        .name {
            font-size: 36px;
            font-weight: bold;
            font-style: italic;
            margin-bottom: 15px;
            color: #2c2c2c;
        }

        .contact-info {
            font-size: 14px;
            line-height: 1.6;
        }

        .contact-info div {
            margin-bottom: 3px;
        }

        .contact-label {
            font-weight: bold;
            display: inline-block;
            width: 60px;
        }

        .header-right {
            margin-left: 30px;
        }

        .profile-photo {
            width: 120px;
            height: 140px;
            border: 1px solid #ccc;
            background-color: #666;
        }

        .download-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .download-btn {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .download-btn:hover {
            background-color: #0052a3;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            font-style: italic;
            margin-bottom: 15px;
            color: #2c2c2c;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .portfolio-link {
            font-size: 14px;
            margin-bottom: 15px;
            text-align: center;
        }

        .portfolio-link a {
            color: #0066cc;
            text-decoration: none;
        }

        .description {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            text-align: justify;
        }

        .skills-list {
            list-style: none;
            padding: 0;
        }

        .skills-list li {
            margin-bottom: 4px;
            position: relative;
            padding-left: 15px;
            font-size: 14px;
        }

        .skills-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .work-entry {
            margin-bottom: 25px;
        }

        .work-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 5px;
        }

        .work-dates {
            font-weight: bold;
            font-size: 14px;
        }

        .work-title {
            font-weight: bold;
            font-size: 16px;
            color: #2c2c2c;
        }

        .work-subtitle {
            font-style: italic;
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .work-details {
            list-style: none;
            padding: 0;
        }

        .work-details li {
            margin-bottom: 6px;
            position: relative;
            padding-left: 15px;
            font-size: 14px;
            line-height: 1.4;
        }

        .work-details li:before {
            content: "•";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .education-entry {
            margin-bottom: 15px;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 5px;
        }

        .education-dates {
            font-weight: bold;
            font-size: 14px;
        }

        .education-title {
            font-weight: bold;
            font-size: 16px;
            color: #2c2c2c;
        }

        .education-subtitle {
            font-style: italic;
            font-size: 14px;
            color: #666;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #888;
            text-align: center;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #666;
            text-decoration: none;
            text-transform: uppercase;
            font-size: 11px;
        }

        /* Print styles */
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            .resume-container {
                box-shadow: none;
                padding: 20px;
            }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="download-buttons">
        <button class="download-btn" onclick="downloadPDF()">Download PDF</button>
        <button class="download-btn" onclick="downloadDOCX()">Download DOCX</button>
    </div>
    <div class="resume-container">
        <div class="header">
            <div class="header-left">
                <div class="name">Charles Attoh</div>
                <div class="contact-info">
                    <div><span class="contact-label">Address</span> Dansoman, Ghana 00233</div>
                    <div><span class="contact-label">Phone</span> +233 50 648 2495</div>
                    <div><span class="contact-label">E-mail</span> <EMAIL></div>
                </div>
            </div>
            <div class="header-right">
                <div class="profile-photo" style="background-image: url('profilehoto - here'); background-size: cover; background-position: center; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;">
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Websites, Portfolios, Profiles</div>
            <div class="portfolio-link">
                <a href="https://github.com/RedWilly" target="_blank">• github.com/RedWilly</a>
            </div>
            <div class="description">
                Reliable and adaptable worker with experience in technical trades and freelance projects. Comfortable with general labour, cleaning, customer service, and kitchen work, and also capable of web-based development if required. Quick to learn, safety-minded, dependable, and ready to relocate to Canada for full time work.
            </div>
        </div>

        <div class="section">
            <div class="section-title">Skills</div>
            <ul class="skills-list">
                <li>Web development (5 years)</li>
                <li>Warehouse operations</li>
                <li>Bartending basics</li>
                <li>Backend development (4 years)</li>
                <li>Dispatcher / scheduling</li>
                <li>Roofing installation & repair (2 years)</li>
                <li>Loading & unloading</li>
                <li>General Labour</li>
                <li>AI Agentic framework (Less than 1 year)</li>
                <li>Housekeeping (1 year)</li>
                <li>Object-oriented programming</li>
            </ul>
        </div>

        <div class="section">
            <div class="section-title">Work History</div>
            <div class="work-entry">
                <div class="work-header">
                    <span class="work-dates">2019-09 - Current</span>
                    <span class="work-title">Freelance Developer</span>
                </div>
                <div class="work-subtitle">Self-Employed | Fiverr & GitHub Contributions, Dansoman, Accra</div>
                <ul class="work-details">
                    <li>Delivered 70+ freelance projects on Fiverr (fiverr.com/willvp) with an average rating of 4.9/5.</li>
                    <li>Built and maintained TypeScript tools, automation scripts, and backend documentation.</li>
                    <li>Contributed to multiple open source projects on GitHub (@RedWilly) including Munny (animation engine), Scene-Visualize (script-to-video pipeline), and Num.js (numeric computing library).</li>
                    <li>Collaborated with international clients, managed deadlines, and delivered quality results on Fiverr (fiverr.com/willvp).</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <div class="section-title">EDUC</div>
            <div class="education-entry">
                <div class="education-header">
                    <span class="education-dates">2017-07</span>
                </div>
                <div class="education-title">High School Diploma: Electrical, Electronics And Communications Engineering</div>
                <div class="education-subtitle">Cape Coast Technical Institute - Central Region</div>
            </div>
        </div>


    </div>
</body>
</html>